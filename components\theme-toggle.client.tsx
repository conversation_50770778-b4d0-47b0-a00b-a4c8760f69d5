import { useCookie } from 'blade/hooks';
import { useCallback, useEffect, useState } from 'react';
import { WiMoonAltNew } from "react-icons/wi";
import { Tooltip } from './ui/tooltip1-client';
import BorderDark from './ui/border-dark.client';
import BorderLight from './ui/border-light.client';

export type Theme = 'light' | 'dark' | 'system';

interface ThemeToggleProps {
  initial?: Theme | null;
  disableTooltips?: boolean;
}



export function ThemeToggle(props: ThemeToggleProps) {
  const [currentTheme, setCurrentTheme] = useState<Theme>(props.initial ?? 'system');
  const [themeCookie, setThemeCookie] = useCookie<Theme>('theme');

  // Initialize theme state from cookie or props
  useEffect(() => {
    if (themeCookie) {
      setCurrentTheme(themeCookie);
    } else if (props.initial) {
      setCurrentTheme(props.initial);
    }
  }, [themeCookie, props.initial]);

  const setTheme = useCallback(
    (theme: Theme): void => {
      setCurrentTheme(theme);
      setThemeCookie(theme, { client: true });

      if (theme === 'dark') {
        document.documentElement.classList.add('dark');
        document.documentElement.classList.remove('light');
        localStorage.setItem('theme', 'dark');
        localStorage.setItem('darkMode', 'dark');
      } else if (theme === 'light') {
        document.documentElement.classList.remove('dark');
        document.documentElement.classList.add('light');
        localStorage.setItem('theme', 'light');
        localStorage.setItem('darkMode', 'light');
      } else {
        document.documentElement.classList.remove('dark');
        document.documentElement.classList.remove('light');
        localStorage.removeItem('theme');
        localStorage.removeItem('darkMode');
      }
    },
    [setThemeCookie],
  );

  // Common button classes for both light and dark mode buttons
  const buttonClasses =
    "z-1 overflow-hidden cursor-pointer animate-pop gap-2 flex font-manrope_1 items-center";

  const renderLightModeButton = () => (
    <button type="button" className={buttonClasses} onClick={() => setTheme('light')}>
      <span className="absolute inset-[1px] transition-all duration-200"></span>
      <span className="font-manrope_1 text z-10 dark:text-[#e9e5dc] text-[#1e1b16] text-[22px]">
        <div className="hidden dark:block">
          <BorderLight component={() => <WiMoonAltNew className="w-3 h-3" />} />
        </div>
      </span>
    </button>
  );

  const renderDarkModeButton = () => (
    <button type="button" className={buttonClasses} onClick={() => setTheme('dark')}>
      <span className="absolute inset-[1px] transition-all duration-200"></span>
      <span className="font-manrope_1 z-10 text-xs text-[#1e1b16]">
        <div className="block dark:hidden">
          <BorderDark component={() => <WiMoonAltNew className="w-3 h-3" />} />
        </div>
      </span>
    </button>
  );

  return (
    <div className="relative">
      <div className="items-center justify-center">
        <div className="flex items-center">
          {/* Light mode button with tooltip only in dark mode */}
          {currentTheme === 'dark' ? (
            props.disableTooltips ? (
              renderLightModeButton()
            ) : (
              <Tooltip text="Switch to light mode">{renderLightModeButton()}</Tooltip>
            )
          ) : (
            renderLightModeButton()
          )}

          {/* Dark mode button with tooltip only in light mode */}
          {currentTheme === 'light' ? (
            props.disableTooltips ? (
              renderDarkModeButton()
            ) : (
              <Tooltip text="Switch to dark mode">{renderDarkModeButton()}</Tooltip>
            )
          ) : (
            renderDarkModeButton()
          )}
        </div>
      </div>
    </div>
  );
}