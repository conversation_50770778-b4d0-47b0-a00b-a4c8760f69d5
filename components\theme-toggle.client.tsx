import { useCookie } from 'blade/hooks';
import { useCallback, useEffect, useState } from 'react';
import { WiMoonAltNew } from "react-icons/wi";


import { Tooltip } from './ui/tooltip1-client';
import BorderDark from './ui/border-dark';
import BorderLight from './ui/border-light';

export type Theme = 'light' | 'dark' | 'system';

interface ThemeToggleProps {
  initial?: Theme | null;
  disableTooltips?: boolean;
}

function updateMode(isDark: boolean) {
  // Update localStorage and cookie
  localStorage.setItem('theme', isDark ? 'dark' : 'light');
  localStorage.setItem('darkMode', isDark ? 'dark' : 'light');

  // Apply dark mode class to document
  if (typeof document !== 'undefined') {
    document.documentElement.classList[isDark ? 'add' : 'remove']('dark');
    document.dispatchEvent(new Event('dark'));
    document.cookie = `dark=${isDark ? 1 : 0}; path=/`;
  }
}

export function ThemeToggle(props: ThemeToggleProps) {
  const [mode, setMode] = useState<Theme>(props.initial ?? 'system');
  const [themeCookie, setThemeCookie] = useCookie<Theme>('theme');

  // Initialize theme state from localStorage or system preference
  useEffect(() => {
    // Check for the stored mode in localStorage on component mount
    const storedMode = localStorage.getItem('darkMode') || localStorage.getItem('theme');
    if (storedMode) {
      setMode(storedMode === 'dark' ? 'dark' : 'light');
    } else if (props.initial === 'system') {
      // If no stored preference and initial is "system", use system preference
      const isDarkMode = globalThis.matchMedia('(prefers-color-scheme: dark)').matches;
      setMode(isDarkMode ? 'dark' : 'light');
    }
  }, [props.initial]);

  // Set dark mode using our utility function
  const setDarkMode = useCallback((isDark: boolean) => {
    const newTheme: Theme = isDark ? 'dark' : 'light';
    setMode(newTheme);
    setThemeCookie(newTheme, { client: true });
    updateMode(isDark);
  }, [setThemeCookie]);

  // Common button classes for both light and dark mode buttons
  const buttonClasses =
    "z-1 overflow-hidden cursor-pointer animate-pop gap-2 flex font-manrope_1 items-center";

  const renderLightModeButton = () => (
    <button type="button" className={buttonClasses} onClick={() => setDarkMode(false)}>
      <span className="absolute inset-[1px] transition-all duration-200"></span>
      <span className="font-manrope_1 text z-10 dark:text-[#e9e5dc] text-[#1e1b16] text-[22px]">
        <div className="hidden dark:block">
          <BorderLight component={() => <WiMoonAltNew className="w-3 h-3" />} />
        </div>
      </span>
    </button>
  );

  const renderDarkModeButton = () => (
    <button type="button" className={buttonClasses} onClick={() => setDarkMode(true)}>
      <span className="absolute inset-[1px] transition-all duration-200"></span>
      <span className="font-manrope_1 z-10 text-xs text-[#1e1b16]">
        <div className="block dark:hidden">
          <BorderDark component={() => <WiMoonAltNew className="w-3 h-3" />} />
        </div>
      </span>
    </button>
  );

  return (
    <div className="relative">
      <div className="items-center justify-center">
        <div className="flex items-center">
          {/* Light mode button with tooltip only in dark mode */}
          {mode === 'dark' ? (
            props.disableTooltips ? (
              renderLightModeButton()
            ) : (
              <Tooltip text="Switch to light mode">{renderLightModeButton()}</Tooltip>
            )
          ) : (
            renderLightModeButton()
          )}

          {/* Dark mode button with tooltip only in light mode */}
          {mode === 'light' ? (
            props.disableTooltips ? (
              renderDarkModeButton()
            ) : (
              <Tooltip text="Switch to dark mode">{renderDarkModeButton()}</Tooltip>
            )
          ) : (
            renderDarkModeButton()
          )}
        </div>
      </div>
    </div>
  );
}