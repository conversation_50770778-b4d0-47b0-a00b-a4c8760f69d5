import { useEffect, useState } from 'react';

interface BorderAnimationProps {
  component: () => React.ReactElement;
  className?: string;
}

function BorderDark({ component, className }: BorderAnimationProps) {
  const [angle, setAngle] = useState(0);

  useEffect(() => {
    const updateAnimation = () => {
      setAngle((prevAngle) => (prevAngle + 0.5) % 360);
      requestAnimationFrame(updateAnimation);
    };

    const animationId = requestAnimationFrame(updateAnimation);
    
    return () => cancelAnimationFrame(animationId);
  }, []);

  useEffect(() => {
    document.documentElement.style.setProperty('--angle', `${angle}deg`);
  }, [angle]);

  return <div className={`box_dark ${className || ""}`}>{component()}</div>;
}

export default BorderDark;
